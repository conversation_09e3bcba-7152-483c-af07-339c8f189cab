<?php
/**
 * إصلاح سريع لنظام الصلاحيات
 * يقوم بإضافة البيانات الناقصة فقط دون حذف الموجود
 */

include 'db_connection.php';
include 'encryption_functions.php';

// تعيين ترميز قاعدة البيانات
$conn->set_charset("utf8mb4");

echo "<h2>🔧 إصلاح سريع لنظام الصلاحيات</h2>";

// 1. فحص وإضافة الوحدات الناقصة
echo "<h3>1. فحص الوحدات:</h3>";
$cashier_modules = [
    ['cashier_home', 'الرئيسية', 'الصفحة الرئيسية لنظام الكاشير', 'users.php', 'fas fa-home', 17],
    ['cashier_invoices', 'إضافة الفواتير', 'إضافة فواتير الشراء والبيع', 'add_invoice.php', 'fas fa-file-invoice', 18],
    ['cashier_shift_closure', 'تقفيل الوردية', 'تقفيل وردية الكاشير', 'shift_closure.php', 'fas fa-cash-register', 19],
    ['cashier_account', 'إدارة الحساب', 'إدارة بيانات الحساب الشخصي', 'user_account.php', 'fas fa-user-cog', 20]
];

foreach ($cashier_modules as $module) {
    $check = $conn->query("SELECT COUNT(*) as count FROM modules WHERE module_name = '{$module[0]}'");
    $exists = $check->fetch_assoc()['count'] > 0;
    
    if (!$exists) {
        $stmt = $conn->prepare("INSERT INTO modules (module_name, module_name_ar, description, file_path, icon_class, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sssssi", $module[0], $module[1], $module[2], $module[3], $module[4], $module[5]);
        if ($stmt->execute()) {
            echo "✅ تم إضافة وحدة: {$module[1]}<br>";
        }
        $stmt->close();
    } else {
        echo "✅ وحدة {$module[1]} موجودة<br>";
    }
}

// 2. فحص وإضافة الصلاحيات الناقصة
echo "<h3>2. فحص الصلاحيات:</h3>";
$cashier_permissions = [
    ['create_purchase', 'إنشاء فاتورة شراء', 'صلاحية إنشاء فواتير الشراء في نظام الكاشير'],
    ['create_sale', 'إنشاء فاتورة بيع', 'صلاحية إنشاء فواتير البيع بالجملة في نظام الكاشير'],
    ['close_shift', 'تقفيل الوردية', 'صلاحية تقفيل الوردية في نظام الكاشير'],
    ['edit_profile', 'تعديل البيانات الشخصية', 'صلاحية تعديل البيانات الشخصية'],
    ['switch_store', 'التبديل بين الفروع', 'صلاحية التبديل بين الفروع المختلفة']
];

foreach ($cashier_permissions as $permission) {
    $check = $conn->query("SELECT COUNT(*) as count FROM permissions WHERE permission_name = '{$permission[0]}'");
    $exists = $check->fetch_assoc()['count'] > 0;
    
    if (!$exists) {
        $stmt = $conn->prepare("INSERT INTO permissions (permission_name, permission_name_ar, description) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $permission[0], $permission[1], $permission[2]);
        if ($stmt->execute()) {
            echo "✅ تم إضافة صلاحية: {$permission[1]}<br>";
        }
        $stmt->close();
    } else {
        echo "✅ صلاحية {$permission[1]} موجودة<br>";
    }
}

// 3. إصلاح جدول module_permissions
echo "<h3>3. إصلاح ربط الوحدات بالصلاحيات:</h3>";
$current_count = $conn->query("SELECT COUNT(*) as count FROM module_permissions")->fetch_assoc()['count'];
echo "عدد الروابط الحالية: $current_count<br>";

if ($current_count == 0) {
    echo "🔧 إضافة الروابط الناقصة...<br>";
    
    $module_permission_mappings = [
        // وحدات النظام الإداري
        ['dashboard', ['access', 'view']],
        ['store_management', ['access', 'view', 'manage']],
        ['reports', ['access', 'view', 'export', 'reports']],
        ['categories', ['access', 'view', 'create', 'edit', 'delete']],
        ['items', ['access', 'view', 'create', 'edit', 'delete', 'export', 'import']],
        ['purchase_invoices', ['access', 'view', 'create', 'edit', 'delete', 'export', 'approve']],
        ['wholesale_invoices', ['access', 'view', 'create', 'edit', 'delete', 'export', 'approve']],
        ['inventory', ['access', 'view', 'create', 'edit', 'export', 'reports']],
        ['accounts', ['access', 'view', 'create', 'edit', 'delete', 'manage']],
        ['transfer_items', ['access', 'view', 'create', 'approve']],
        ['expired_items', ['access', 'view', 'edit', 'reports']],
        ['expenses', ['access', 'view', 'create', 'edit', 'delete', 'export']],
        ['shift_closures', ['access', 'view', 'create', 'export', 'reports']],
        ['balance_transfers', ['access', 'view', 'create', 'approve']],
        ['notifications', ['access', 'view', 'create', 'edit', 'delete']],
        ['send_notifications', ['access', 'create']],
        
        // وحدات نظام الكاشير
        ['cashier_home', ['access', 'view']],
        ['cashier_invoices', ['access', 'view', 'create_purchase', 'create_sale']],
        ['cashier_shift_closure', ['access', 'view', 'close_shift']],
        ['cashier_account', ['access', 'view', 'edit_profile', 'switch_store']]
    ];
    
    $added_count = 0;
    foreach ($module_permission_mappings as $mapping) {
        $module_name = $mapping[0];
        $permissions = $mapping[1];
        
        // الحصول على معرف الوحدة
        $module_result = $conn->query("SELECT module_id FROM modules WHERE module_name = '$module_name'");
        if ($module_row = $module_result->fetch_assoc()) {
            $module_id = $module_row['module_id'];
            
            foreach ($permissions as $permission_name) {
                // الحصول على معرف الصلاحية
                $permission_result = $conn->query("SELECT permission_id FROM permissions WHERE permission_name = '$permission_name'");
                if ($permission_row = $permission_result->fetch_assoc()) {
                    $permission_id = $permission_row['permission_id'];
                    
                    // إدراج الربط
                    $insert_query = "INSERT IGNORE INTO module_permissions (module_id, permission_id, is_required) VALUES ($module_id, $permission_id, TRUE)";
                    if ($conn->query($insert_query)) {
                        $added_count++;
                    }
                }
            }
        }
    }
    echo "✅ تم إضافة $added_count ربط وحدة-صلاحية<br>";
} else {
    echo "✅ الروابط موجودة مسبقاً<br>";
}

// 4. فحص عمود access_type
echo "<h3>4. فحص عمود نوع الوصول:</h3>";
$check_column = $conn->query("SHOW COLUMNS FROM accounts LIKE 'access_type'");
if ($check_column->num_rows == 0) {
    $add_column = "ALTER TABLE accounts ADD COLUMN access_type ENUM('admin_panel', 'cashier_system') DEFAULT 'cashier_system' AFTER role";
    if ($conn->query($add_column)) {
        echo "✅ تم إضافة عمود access_type<br>";
    }
} else {
    echo "✅ عمود access_type موجود<br>";
}

// 5. تحديث أنواع الوصول
$update_non_admins = "UPDATE accounts SET access_type = 'cashier_system' WHERE role != 'admin' AND (access_type IS NULL OR access_type = '')";
$conn->query($update_non_admins);

$update_admins = "UPDATE accounts SET access_type = 'admin_panel' WHERE role = 'admin'";
$conn->query($update_admins);

echo "✅ تم تحديث أنواع الوصول<br>";

// 6. إضافة صلاحيات نظام الكاشير للمستخدمين العاديين
echo "<h3>5. إضافة صلاحيات نظام الكاشير:</h3>";

$cashier_queries = [
    "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
     SELECT 4, m.module_id, p.permission_id, TRUE
     FROM modules m CROSS JOIN permissions p
     WHERE m.module_name = 'cashier_home' AND p.permission_name IN ('access', 'view')",
     
    "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
     SELECT 4, m.module_id, p.permission_id, TRUE
     FROM modules m CROSS JOIN permissions p
     WHERE m.module_name = 'cashier_invoices' AND p.permission_name IN ('access', 'view', 'create_purchase', 'create_sale')",
     
    "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
     SELECT 4, m.module_id, p.permission_id, TRUE
     FROM modules m CROSS JOIN permissions p
     WHERE m.module_name = 'cashier_shift_closure' AND p.permission_name IN ('access', 'view', 'close_shift')",
     
    "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
     SELECT 4, m.module_id, p.permission_id, TRUE
     FROM modules m CROSS JOIN permissions p
     WHERE m.module_name = 'cashier_account' AND p.permission_name IN ('access', 'view', 'edit_profile', 'switch_store')"
];

$total_added = 0;
foreach ($cashier_queries as $query) {
    if ($conn->query($query)) {
        $total_added += $conn->affected_rows;
    }
}

echo "✅ تم إضافة $total_added صلاحية جديدة لنظام الكاشير<br>";

echo "<h3>✅ انتهى الإصلاح السريع</h3>";
echo "<p><strong>النتيجة:</strong> تم إصلاح جميع البيانات الناقصة في نظام الصلاحيات</p>";

$conn->close();
?>
