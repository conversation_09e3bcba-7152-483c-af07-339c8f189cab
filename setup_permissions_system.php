<?php
/**
 * ملف تكوين نظام الصلاحيات
 * يقوم بإعداد وتكوين نظام الصلاحيات الكامل للمشروع
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين الملفات المطلوبة
include_once 'db_connection.php';
include_once 'encryption_functions.php';

// تعيين ترميز قاعدة البيانات
if (isset($conn)) {
    $conn->set_charset("utf8mb4");
}

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// التحقق من صلاحية المدير
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('يجب أن تكون مدير للوصول لهذه الصفحة');
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // تنظيف أي output سابق
    if (ob_get_level()) {
        ob_clean();
    }

    header('Content-Type: application/json; charset=utf-8');

    try {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'check_database':
                $result = checkAndCreateTables($conn);
                echo json_encode(['success' => true, 'step' => 1, 'message' => 'تم فحص وإنشاء الجداول بنجاح', 'details' => $result]);
                break;

            case 'create_modules':
                $result = createModules($conn);
                echo json_encode(['success' => true, 'step' => 2, 'message' => 'تم إنشاء الوحدات بنجاح', 'details' => $result]);
                break;

            case 'create_permissions':
                $result = createPermissions($conn);
                echo json_encode(['success' => true, 'step' => 3, 'message' => 'تم إنشاء الصلاحيات بنجاح', 'details' => $result]);
                break;

            case 'setup_module_permissions':
                $result = setupModulePermissions($conn);
                echo json_encode(['success' => true, 'step' => 4, 'message' => 'تم تكوين ربط الوحدات بالصلاحيات بنجاح', 'details' => $result]);
                break;

            case 'setup_role_permissions':
                $result = setupRolePermissions($conn);
                echo json_encode(['success' => true, 'step' => 5, 'message' => 'تم تكوين صلاحيات الأدوار بنجاح', 'details' => $result]);
                break;

            case 'setup_admin_permissions':
                $result = setupAdminPermissions($conn);
                echo json_encode(['success' => true, 'step' => 6, 'message' => 'تم إنشاء صلاحيات المدير بنجاح', 'details' => $result]);
                break;

            case 'setup_access_types':
                $result = setupAccessTypes($conn);
                echo json_encode(['success' => true, 'step' => 7, 'message' => 'تم تكوين نوع الوصول بنجاح', 'details' => $result]);
                break;

            case 'test_system':
                $result = testSystem($conn, $key);
                echo json_encode(['success' => true, 'step' => 8, 'message' => 'تم اختبار النظام بنجاح', 'details' => $result]);
                break;

            case 'reset_system':
                $result = resetSystem($conn);
                echo json_encode(['success' => true, 'message' => 'تم إعادة تعيين النظام بنجاح', 'details' => $result]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير معروف: ' . $action]);
        }
    } catch (Exception $e) {
        error_log("خطأ في تكوين نظام الصلاحيات - الإجراء: $action - الخطأ: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'action' => $action
        ]);
    } catch (Error $e) {
        error_log("خطأ فادح في تكوين نظام الصلاحيات - الإجراء: $action - الخطأ: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ فادح: ' . $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'action' => $action
        ]);
    }
    exit;
}

/**
 * دوال التكوين
 */

function checkAndCreateTables($conn) {
    $results = [];

    try {
        // فحص الجداول الموجودة
        $existing_tables = [];
        $tables_result = $conn->query("SHOW TABLES");
        if ($tables_result) {
            while ($row = $tables_result->fetch_array()) {
                $existing_tables[] = $row[0];
            }
        }

        $results[] = "📊 تم العثور على " . count($existing_tables) . " جدول موجود";

        // 1. فحص وتعديل جدول الحسابات
        $results[] = "🔍 فحص جدول الحسابات...";

        // فحص وجود عمود access_type
        $check_column = "SHOW COLUMNS FROM accounts LIKE 'access_type'";
        $result = $conn->query($check_column);

        if ($result && $result->num_rows == 0) {
            $add_column = "ALTER TABLE accounts ADD COLUMN access_type ENUM('admin_panel', 'cashier_system') DEFAULT 'cashier_system' AFTER role";
            if ($conn->query($add_column)) {
                $results[] = "✅ تم إضافة عمود access_type لجدول الحسابات (افتراضي: نظام الكاشير)";
            } else {
                $results[] = "❌ خطأ في إضافة عمود access_type: " . $conn->error;
            }
        } else {
            $results[] = "✅ عمود access_type موجود في جدول الحسابات";
        }

        // تحديث الحسابات الموجودة لتكون افتراضياً نظام الكاشير (عدا المديرين)
        $update_existing = "UPDATE accounts SET access_type = 'cashier_system' WHERE role != 'admin'";
        if ($conn->query($update_existing)) {
            $results[] = "✅ تم تحديث الحسابات الموجودة لنظام الكاشير (عدا المديرين)";
        } else {
            $results[] = "❌ خطأ في تحديث الحسابات: " . $conn->error;
        }

        // تحديث المديرين ليكونوا في النظام الإداري
        $update_admins = "UPDATE accounts SET access_type = 'admin_panel' WHERE role = 'admin'";
        if ($conn->query($update_admins)) {
            $results[] = "✅ تم تحديث المديرين للنظام الإداري";
        } else {
            $results[] = "❌ خطأ في تحديث المديرين: " . $conn->error;
        }

        // تحديث ترميز جدول accounts ليتطابق مع الجداول الجديدة
        $update_charset = "ALTER TABLE accounts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
        if ($conn->query($update_charset)) {
            $results[] = "✅ تم تحديث ترميز جدول الحسابات ليتطابق مع النظام";
        } else {
            $results[] = "⚠️ تعذر تحديث ترميز جدول الحسابات: " . $conn->error;
        }

        // تحديث ترميز جدول stores أيضاً
        $update_stores_charset = "ALTER TABLE stores CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
        if ($conn->query($update_stores_charset)) {
            $results[] = "✅ تم تحديث ترميز جدول الفروع ليتطابق مع النظام";
        } else {
            $results[] = "⚠️ تعذر تحديث ترميز جدول الفروع: " . $conn->error;
        }

        // 2. إنشاء جدول الأدوار إذا لم يكن موجوداً
        if (in_array('roles', $existing_tables)) {
            $results[] = "✅ جدول الأدوار موجود مسبقاً";
        } else {
            $create_roles = "CREATE TABLE IF NOT EXISTS roles (
            role_id INT AUTO_INCREMENT PRIMARY KEY,
            role_name VARCHAR(50) NOT NULL UNIQUE,
            role_name_ar VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

            if ($conn->query($create_roles)) {
                $results[] = "✅ جدول الأدوار جاهز";
            } else {
                $results[] = "❌ خطأ في إنشاء جدول الأدوار: " . $conn->error;
            }
        }

        // 3. إنشاء جدول الوحدات
        if (in_array('modules', $existing_tables)) {
            $results[] = "✅ جدول الوحدات موجود مسبقاً";
        } else {
            $create_modules = "CREATE TABLE IF NOT EXISTS modules (
            module_id INT AUTO_INCREMENT PRIMARY KEY,
            module_name VARCHAR(100) NOT NULL UNIQUE,
            module_name_ar VARCHAR(150) NOT NULL,
            description TEXT,
            file_path VARCHAR(255),
            icon_class VARCHAR(100),
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

            if ($conn->query($create_modules)) {
                $results[] = "✅ جدول الوحدات جاهز";
            } else {
                $results[] = "❌ خطأ في إنشاء جدول الوحدات: " . $conn->error;
            }
        }

        // 4. إنشاء جدول الصلاحيات
        if (in_array('permissions', $existing_tables)) {
            $results[] = "✅ جدول الصلاحيات موجود مسبقاً";
        } else {
            $create_permissions = "CREATE TABLE IF NOT EXISTS permissions (
            permission_id INT AUTO_INCREMENT PRIMARY KEY,
            permission_name VARCHAR(100) NOT NULL UNIQUE,
            permission_name_ar VARCHAR(150) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

            if ($conn->query($create_permissions)) {
                $results[] = "✅ جدول الصلاحيات جاهز";
            } else {
                $results[] = "❌ خطأ في إنشاء جدول الصلاحيات: " . $conn->error;
            }
        }

        // 5. إنشاء جدول صلاحيات الأدوار
        $create_role_permissions = "CREATE TABLE IF NOT EXISTS role_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_id INT NOT NULL,
            module_id INT NOT NULL,
            permission_id INT NOT NULL,
            granted BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_role_module_permission (role_id, module_id, permission_id),
            FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        if ($conn->query($create_role_permissions)) {
            $results[] = "✅ جدول صلاحيات الأدوار جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول صلاحيات الأدوار: " . $conn->error;
        }

        // 6. إنشاء جدول صلاحيات المستخدمين
        $create_user_permissions = "CREATE TABLE IF NOT EXISTS user_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_id INT NOT NULL,
            module_id INT NOT NULL,
            permission_id INT NOT NULL,
            granted BOOLEAN DEFAULT FALSE,
            created_by INT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_module_permission (account_id, module_id, permission_id),
            FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES accounts(account_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        if ($conn->query($create_user_permissions)) {
            $results[] = "✅ جدول صلاحيات المستخدمين جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول صلاحيات المستخدمين: " . $conn->error;
        }

        // 7. إنشاء جدول فروع المستخدمين
        $create_user_stores = "CREATE TABLE IF NOT EXISTS user_stores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            store_id INT NOT NULL,
            granted BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_store (user_id, store_id),
            FOREIGN KEY (user_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
            FOREIGN KEY (store_id) REFERENCES stores(store_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        if ($conn->query($create_user_stores)) {
            $results[] = "✅ جدول فروع المستخدمين جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول فروع المستخدمين: " . $conn->error;
        }

        // 8. إنشاء جدول ربط الوحدات بالصلاحيات
        $create_module_permissions = "CREATE TABLE IF NOT EXISTS module_permissions (
            module_permission_id INT AUTO_INCREMENT PRIMARY KEY,
            module_id INT NOT NULL,
            permission_id INT NOT NULL,
            is_required BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
            UNIQUE KEY unique_module_permission (module_id, permission_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        if ($conn->query($create_module_permissions)) {
            $results[] = "✅ جدول ربط الوحدات بالصلاحيات جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول ربط الوحدات بالصلاحيات: " . $conn->error;
        }

        // 9. إنشاء جدول سجل الصلاحيات
        $create_permission_logs = "CREATE TABLE IF NOT EXISTS permission_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            action_type ENUM('grant', 'revoke', 'modify') NOT NULL,
            target_type ENUM('user', 'role') NOT NULL,
            target_id INT NOT NULL,
            module_id INT,
            permission_id INT,
            old_value BOOLEAN,
            new_value BOOLEAN,
            performed_by INT,
            reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE SET NULL,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE SET NULL,
            FOREIGN KEY (performed_by) REFERENCES accounts(account_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        if ($conn->query($create_permission_logs)) {
            $results[] = "✅ جدول سجل الصلاحيات جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول سجل الصلاحيات: " . $conn->error;
        }

        // 10. إدراج الأدوار الأساسية إذا لم تكن موجودة
        $roles_data = [
            [1, 'admin', 'مدير النظام', 'مدير النظام الرئيسي مع جميع الصلاحيات'],
            [2, 'store_manager', 'مدير الفرع', 'مدير فرع مع صلاحيات إدارة الفرع'],
            [3, 'purchaser', 'المشتري', 'مسؤول عن عمليات الشراء'],
            [4, 'user', 'مستخدم عادي', 'مستخدم عادي مع صلاحيات محدودة'],
            [5, 'cashier', 'كاشير', 'كاشير مع صلاحيات نظام الكاشير'],
            [6, 'accountant', 'محاسب', 'محاسب مع صلاحيات المحاسبة']
        ];

        foreach ($roles_data as $role) {
            $check_role = "SELECT role_id FROM roles WHERE role_id = ?";
            $stmt = $conn->prepare($check_role);
            $stmt->bind_param("i", $role[0]);
            $stmt->execute();
            $role_result = $stmt->get_result();

            if ($role_result->num_rows == 0) {
                $stmt->close();
                $insert_role = "INSERT INTO roles (role_id, role_name, role_name_ar, description) VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_role);
                if ($stmt) {
                    $stmt->bind_param("isss", $role[0], $role[1], $role[2], $role[3]);
                    if ($stmt->execute()) {
                        $results[] = "✅ تم إضافة دور: {$role[2]}";
                    } else {
                        $results[] = "❌ خطأ في إضافة دور {$role[2]}: " . $stmt->error;
                    }
                    $stmt->close();
                } else {
                    $results[] = "❌ خطأ في تحضير استعلام إضافة الدور: " . $conn->error;
                }
            } else {
                $stmt->close();
                $results[] = "✅ الدور موجود: {$role[2]}";
            }
        }

        $results[] = "🎉 تم إنشاء وفحص جميع الجداول بنجاح";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في إنشاء الجداول: " . $e->getMessage();
        $results[] = "📍 الملف: " . $e->getFile() . " السطر: " . $e->getLine();
        error_log("خطأ في تكوين نظام الصلاحيات: " . $e->getMessage());
    } catch (Error $e) {
        $results[] = "❌ خطأ فادح: " . $e->getMessage();
        $results[] = "📍 الملف: " . $e->getFile() . " السطر: " . $e->getLine();
        error_log("خطأ فادح في تكوين نظام الصلاحيات: " . $e->getMessage());
    }

    return implode('<br>', $results);
}

function createModules($conn) {
    $results = [];

    // فحص الوحدات الموجودة أولاً
    $existing_modules = [];
    $check_result = $conn->query("SELECT module_name FROM modules");
    if ($check_result) {
        while ($row = $check_result->fetch_assoc()) {
            $existing_modules[] = $row['module_name'];
        }
    }
    $results[] = "📊 عدد الوحدات الموجودة: " . count($existing_modules);

    // إنشاء الوحدات الجديدة (فقط الغير موجودة)
    $modules = [
        // وحدات النظام الإداري
        ['dashboard', 'لوحة التحكم', 'الصفحة الرئيسية ولوحة التحكم', 'stores.php', 'fas fa-home', 1],
        ['store_management', 'إدارة الفرع', 'إدارة وتكوين الفرع', 'store_shortcuts.php', 'fas fa-cogs', 2],
        ['reports', 'التقارير', 'تقارير الأصناف والمبيعات', 'item_reports.php', 'fas fa-chart-line', 3],
        ['categories', 'التصنيفات', 'إدارة تصنيفات المنتجات', 'categories.php', 'fas fa-th-list', 4],
        ['items', 'الأصناف', 'إدارة أصناف المنتجات', 'item_store.php', 'fas fa-boxes', 5],
        ['purchase_invoices', 'فواتير الشراء', 'إدارة فواتير الشراء', 'purchase_invoices.php', 'fas fa-file-invoice-dollar', 6],
        ['wholesale_invoices', 'فواتير البيع بالجملة', 'إدارة فواتير البيع بالجملة', 'wholesale_invoices.php', 'fas fa-shopping-cart', 7],
        ['inventory', 'الجرد', 'إدارة الجرد والمخزون', 'inventory.php', 'fas fa-warehouse', 8],
        ['accounts', 'الحسابات', 'إدارة حسابات المستخدمين', 'accounts.php', 'fas fa-user-cog', 9],
        ['transfer_items', 'نقل الأصناف', 'نقل الأصناف بين الفروع', 'transfer_items.php', 'fas fa-exchange-alt', 10],
        ['expired_items', 'صلاحيات الأصناف', 'إدارة صلاحيات الأصناف', 'expired_items.php', 'fas fa-hourglass-end', 11],
        ['expenses', 'المصاريف', 'إدارة مصاريف الفرع', 'expenses.php', 'fas fa-money-bill-wave', 12],
        ['shift_closures', 'تقفيل الورديات', 'إدارة تقفيل الورديات', 'shift_closures.php', 'fas fa-cash-register', 13],
        ['balance_transfers', 'تحويلات الرصيد', 'إدارة تحويلات الرصيد', 'balance_transfers.php', 'fas fa-wallet', 14],
        ['notifications', 'الإشعارات', 'إدارة الإشعارات', 'notifications_page.php', 'fas fa-bell', 15],
        ['send_notifications', 'إرسال الإشعارات', 'إرسال إشعارات للمستخدمين', 'send_notifications.php', 'fas fa-paper-plane', 16],

        // وحدات نظام الكاشير
        ['cashier_home', 'الرئيسية', 'الصفحة الرئيسية لنظام الكاشير', 'users.php', 'fas fa-home', 17],
        ['cashier_invoices', 'إضافة الفواتير', 'إضافة فواتير الشراء والبيع', 'add_invoice.php', 'fas fa-file-invoice', 18],
        ['cashier_shift_closure', 'تقفيل الوردية', 'تقفيل وردية الكاشير', 'shift_closure.php', 'fas fa-cash-register', 19],
        ['cashier_account', 'إدارة الحساب', 'إدارة بيانات الحساب الشخصي', 'user_account.php', 'fas fa-user-cog', 20]
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO modules (module_name, module_name_ar, description, file_path, icon_class, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
    $added_count = 0;

    foreach ($modules as $module) {
        if (!in_array($module[0], $existing_modules)) {
            $stmt->bind_param("sssssi", $module[0], $module[1], $module[2], $module[3], $module[4], $module[5]);
            if ($stmt->execute()) {
                $results[] = "➕ تم إنشاء وحدة: {$module[1]}";
                $added_count++;
            }
        } else {
            $results[] = "✅ وحدة {$module[1]} موجودة مسبقاً";
        }
    }

    $stmt->close();
    $results[] = "✅ تم إضافة $added_count وحدة جديدة من أصل " . count($modules) . " وحدة";

    return implode('<br>', $results);
}

function createPermissions($conn) {
    $results = [];

    // فحص الصلاحيات الموجودة أولاً
    $existing_permissions = [];
    $check_result = $conn->query("SELECT permission_name FROM permissions");
    if ($check_result) {
        while ($row = $check_result->fetch_assoc()) {
            $existing_permissions[] = $row['permission_name'];
        }
    }
    $results[] = "📊 عدد الصلاحيات الموجودة: " . count($existing_permissions);

    // إنشاء الصلاحيات الجديدة (فقط الغير موجودة)
    $permissions = [
        ['access', 'الوصول', 'صلاحية الوصول للصفحة أو الوحدة'],
        ['view', 'عرض', 'صلاحية عرض البيانات'],
        ['create', 'إنشاء', 'صلاحية إنشاء عناصر جديدة'],
        ['edit', 'تعديل', 'صلاحية تعديل العناصر الموجودة'],
        ['delete', 'حذف', 'صلاحية حذف العناصر'],
        ['export', 'تصدير', 'صلاحية تصدير البيانات'],
        ['import', 'استيراد', 'صلاحية استيراد البيانات'],
        ['manage', 'إدارة', 'صلاحية إدارة شاملة للوحدة'],
        ['approve', 'موافقة', 'صلاحية الموافقة على العمليات'],
        ['reports', 'التقارير', 'صلاحية عرض وإنشاء التقارير'],

        // صلاحيات خاصة بنظام الكاشير
        ['create_purchase', 'إنشاء فاتورة شراء', 'صلاحية إنشاء فواتير الشراء في نظام الكاشير'],
        ['create_sale', 'إنشاء فاتورة بيع', 'صلاحية إنشاء فواتير البيع بالجملة في نظام الكاشير'],
        ['close_shift', 'تقفيل الوردية', 'صلاحية تقفيل الوردية في نظام الكاشير'],
        ['edit_profile', 'تعديل البيانات الشخصية', 'صلاحية تعديل البيانات الشخصية'],
        ['switch_store', 'التبديل بين الفروع', 'صلاحية التبديل بين الفروع المختلفة']
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO permissions (permission_name, permission_name_ar, description) VALUES (?, ?, ?)");
    $added_count = 0;

    foreach ($permissions as $permission) {
        if (!in_array($permission[0], $existing_permissions)) {
            $stmt->bind_param("sss", $permission[0], $permission[1], $permission[2]);
            if ($stmt->execute()) {
                $results[] = "➕ تم إنشاء صلاحية: {$permission[1]}";
                $added_count++;
            }
        } else {
            $results[] = "✅ صلاحية {$permission[1]} موجودة مسبقاً";
        }
    }

    $stmt->close();
    $results[] = "✅ تم إضافة $added_count صلاحية جديدة من أصل " . count($permissions) . " صلاحية";

    return implode('<br>', $results);
}

function setupModulePermissions($conn) {
    $results = [];

    // فحص وجود جدول module_permissions
    $check_table = $conn->query("SHOW TABLES LIKE 'module_permissions'");
    if ($check_table->num_rows == 0) {
        $results[] = "❌ جدول module_permissions غير موجود";
        return implode('<br>', $results);
    }

    // فحص الربط الموجود
    $existing_count = $conn->query("SELECT COUNT(*) as count FROM module_permissions")->fetch_assoc()['count'];
    $results[] = "📊 عدد الروابط الموجودة: $existing_count";

    // ربط الوحدات بالصلاحيات الأساسية
    $module_permission_mappings = [
        // وحدات النظام الإداري - الصلاحيات الأساسية
        ['dashboard', ['access', 'view']],
        ['store_management', ['access', 'view', 'manage']],
        ['reports', ['access', 'view', 'export', 'reports']],
        ['categories', ['access', 'view', 'create', 'edit', 'delete']],
        ['items', ['access', 'view', 'create', 'edit', 'delete', 'export', 'import']],
        ['purchase_invoices', ['access', 'view', 'create', 'edit', 'delete', 'export', 'approve']],
        ['wholesale_invoices', ['access', 'view', 'create', 'edit', 'delete', 'export', 'approve']],
        ['inventory', ['access', 'view', 'create', 'edit', 'export', 'reports']],
        ['accounts', ['access', 'view', 'create', 'edit', 'delete', 'manage']],
        ['transfer_items', ['access', 'view', 'create', 'approve']],
        ['expired_items', ['access', 'view', 'edit', 'reports']],
        ['expenses', ['access', 'view', 'create', 'edit', 'delete', 'export']],
        ['shift_closures', ['access', 'view', 'create', 'export', 'reports']],
        ['balance_transfers', ['access', 'view', 'create', 'approve']],
        ['notifications', ['access', 'view', 'create', 'edit', 'delete']],
        ['send_notifications', ['access', 'create']],

        // وحدات نظام الكاشير - الصلاحيات المخصصة
        ['cashier_home', ['access', 'view']],
        ['cashier_invoices', ['access', 'view', 'create_purchase', 'create_sale']],
        ['cashier_shift_closure', ['access', 'view', 'close_shift']],
        ['cashier_account', ['access', 'view', 'edit_profile', 'switch_store']]
    ];

    $added_links = 0;
    foreach ($module_permission_mappings as $mapping) {
        $module_name = $mapping[0];
        $permissions = $mapping[1];

        // الحصول على معرف الوحدة
        $module_query = "SELECT module_id FROM modules WHERE module_name = ?";
        $stmt = $conn->prepare($module_query);
        $stmt->bind_param("s", $module_name);
        $stmt->execute();
        $module_result = $stmt->get_result();

        if ($module_row = $module_result->fetch_assoc()) {
            $module_id = $module_row['module_id'];
            $module_links = 0;

            foreach ($permissions as $permission_name) {
                // الحصول على معرف الصلاحية
                $permission_query = "SELECT permission_id FROM permissions WHERE permission_name = ?";
                $perm_stmt = $conn->prepare($permission_query);
                $perm_stmt->bind_param("s", $permission_name);
                $perm_stmt->execute();
                $permission_result = $perm_stmt->get_result();

                if ($permission_row = $permission_result->fetch_assoc()) {
                    $permission_id = $permission_row['permission_id'];

                    // فحص إذا كان الربط موجود مسبقاً
                    $check_query = "SELECT COUNT(*) as count FROM module_permissions WHERE module_id = ? AND permission_id = ?";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->bind_param("ii", $module_id, $permission_id);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();
                    $exists = $check_result->fetch_assoc()['count'] > 0;
                    $check_stmt->close();

                    if (!$exists) {
                        // إدراج الربط الجديد
                        $insert_query = "INSERT INTO module_permissions (module_id, permission_id, is_required) VALUES (?, ?, TRUE)";
                        $insert_stmt = $conn->prepare($insert_query);
                        $insert_stmt->bind_param("ii", $module_id, $permission_id);
                        if ($insert_stmt->execute()) {
                            $module_links++;
                            $added_links++;
                        }
                        $insert_stmt->close();
                    }
                }
                $perm_stmt->close();
            }

            if ($module_links > 0) {
                $results[] = "✅ تم ربط وحدة $module_name بـ $module_links صلاحية جديدة";
            } else {
                $results[] = "✅ وحدة $module_name مربوطة مسبقاً بجميع صلاحياتها";
            }
        } else {
            $results[] = "⚠️ لم يتم العثور على وحدة: $module_name";
        }
        $stmt->close();
    }

    $results[] = "✅ تم إضافة $added_links ربط جديد من أصل " . count($module_permission_mappings) . " وحدة";

    return implode('<br>', $results);
}

function setupRolePermissions($conn) {
    $results = [];

    // فحص صلاحيات الأدوار الموجودة
    $existing_count = $conn->query("SELECT COUNT(*) as count FROM role_permissions")->fetch_assoc()['count'];
    $results[] = "📊 عدد صلاحيات الأدوار الموجودة: $existing_count";

    // إذا كانت هناك صلاحيات موجودة، نسأل المستخدم
    if ($existing_count > 0) {
        $results[] = "⚠️ يوجد $existing_count صلاحية موجودة - سيتم تحديث الناقص فقط";
    }

    // إضافة صلاحيات المدير (جميع الصلاحيات على جميع الوحدات)
    $admin_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                    SELECT 1, m.module_id, p.permission_id, TRUE
                    FROM modules m
                    CROSS JOIN permissions p";
    if ($conn->query($admin_query)) {
        $admin_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $admin_added صلاحية جديدة للمدير";
    }

    // إضافة صلاحيات مدير الفرع
    $store_manager_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                           SELECT 2, m.module_id, p.permission_id, TRUE
                           FROM modules m
                           CROSS JOIN permissions p
                           WHERE m.module_name IN ('dashboard', 'store_management', 'reports', 'categories', 'items', 'purchase_invoices', 'wholesale_invoices', 'inventory', 'transfer_items', 'expired_items', 'expenses', 'shift_closures', 'balance_transfers')
                           AND p.permission_name IN ('access', 'view', 'create', 'edit', 'delete', 'export', 'manage', 'approve', 'reports')";
    if ($conn->query($store_manager_query)) {
        $store_manager_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $store_manager_added صلاحية جديدة لمدير الفرع";
    }

    // إضافة صلاحيات المشتري
    $purchaser_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                       SELECT 3, m.module_id, p.permission_id, TRUE
                       FROM modules m
                       CROSS JOIN permissions p
                       WHERE m.module_name IN ('dashboard', 'items', 'purchase_invoices', 'reports')
                       AND p.permission_name IN ('access', 'view', 'create', 'edit', 'export', 'reports')";
    if ($conn->query($purchaser_query)) {
        $purchaser_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $purchaser_added صلاحية جديدة للمشتري";
    }

    // إضافة صلاحيات المستخدم العادي (للنظام الإداري)
    $user_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                  SELECT 4, m.module_id, p.permission_id, TRUE
                  FROM modules m
                  CROSS JOIN permissions p
                  WHERE m.module_name IN ('dashboard', 'items', 'purchase_invoices', 'wholesale_invoices', 'reports')
                  AND p.permission_name IN ('access', 'view', 'reports')";
    if ($conn->query($user_query)) {
        $user_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $user_added صلاحية جديدة للمستخدم العادي (النظام الإداري)";
    }

    // إضافة صلاحيات نظام الكاشير للمستخدمين العاديين

    // صلاحيات الصفحة الرئيسية للكاشير
    $cashier_home_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                          SELECT 4, m.module_id, p.permission_id, TRUE
                          FROM modules m
                          CROSS JOIN permissions p
                          WHERE m.module_name = 'cashier_home'
                          AND p.permission_name IN ('access', 'view')";
    if ($conn->query($cashier_home_query)) {
        $cashier_home_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $cashier_home_added صلاحية للصفحة الرئيسية للكاشير";
    }

    // صلاحيات إضافة الفواتير (شراء وبيع)
    $cashier_invoices_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                              SELECT 4, m.module_id, p.permission_id, TRUE
                              FROM modules m
                              CROSS JOIN permissions p
                              WHERE m.module_name = 'cashier_invoices'
                              AND p.permission_name IN ('access', 'view', 'create_purchase', 'create_sale')";
    if ($conn->query($cashier_invoices_query)) {
        $cashier_invoices_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $cashier_invoices_added صلاحية لإضافة الفواتير (شراء وبيع)";
    }

    // صلاحيات تقفيل الوردية
    $cashier_shift_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                           SELECT 4, m.module_id, p.permission_id, TRUE
                           FROM modules m
                           CROSS JOIN permissions p
                           WHERE m.module_name = 'cashier_shift_closure'
                           AND p.permission_name IN ('access', 'view', 'close_shift')";
    if ($conn->query($cashier_shift_query)) {
        $cashier_shift_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $cashier_shift_added صلاحية لتقفيل الوردية";
    }

    // صلاحيات إدارة الحساب (تعديل البيانات والتبديل بين الفروع)
    $cashier_account_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                             SELECT 4, m.module_id, p.permission_id, TRUE
                             FROM modules m
                             CROSS JOIN permissions p
                             WHERE m.module_name = 'cashier_account'
                             AND p.permission_name IN ('access', 'view', 'edit_profile', 'switch_store')";
    if ($conn->query($cashier_account_query)) {
        $cashier_account_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $cashier_account_added صلاحية لإدارة الحساب";
    }

    // إضافة صلاحيات المحاسب (إذا كان الدور موجود)
    $accountant_query = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
                        SELECT 6, m.module_id, p.permission_id, TRUE
                        FROM modules m
                        CROSS JOIN permissions p
                        WHERE m.module_name IN ('dashboard', 'purchase_invoices', 'wholesale_invoices', 'expenses', 'reports')
                        AND p.permission_name IN ('access', 'view', 'export', 'reports')";
    if ($conn->query($accountant_query)) {
        $accountant_added = $conn->affected_rows;
        $results[] = "✅ تم إضافة $accountant_added صلاحية للمحاسب";
    }

    return implode('<br>', $results);
}

function setupAdminPermissions($conn) {
    $results = [];

    // التأكد من وجود صلاحيات المدير
    $check_query = "SELECT COUNT(*) as count FROM role_permissions rp
                   JOIN roles r ON rp.role_id = r.role_id
                   WHERE r.role_name COLLATE utf8mb4_general_ci = 'admin' AND rp.granted = TRUE";
    $result = $conn->query($check_query);
    $count = $result->fetch_assoc()['count'];

    $results[] = "📊 عدد صلاحيات المدير: $count";

    if ($count > 0) {
        $results[] = "✅ صلاحيات المدير موجودة ومكتملة";
    } else {
        $results[] = "❌ لم يتم العثور على صلاحيات المدير";
    }

    return implode('<br>', $results);
}

function setupAccessTypes($conn) {
    $results = [];

    try {
        // التأكد من وجود عمود access_type
        $check_column = "SHOW COLUMNS FROM accounts LIKE 'access_type'";
        $result = $conn->query($check_column);

        if ($result->num_rows == 0) {
            // إضافة العمود إذا لم يكن موجوداً (افتراضي: نظام الكاشير)
            $add_column = "ALTER TABLE accounts ADD COLUMN access_type ENUM('admin_panel', 'cashier_system') DEFAULT 'cashier_system' AFTER role";
            $conn->query($add_column);
            $results[] = "✅ تم إضافة عمود نوع الوصول (افتراضي: نظام الكاشير)";
        } else {
            $results[] = "✅ عمود نوع الوصول موجود مسبقاً";
        }

        // تحديث جميع المستخدمين غير المديرين ليكون وصولهم لنظام الكاشير
        $update_non_admins = "UPDATE accounts SET access_type = 'cashier_system' WHERE role != 'admin'";
        $conn->query($update_non_admins);
        $results[] = "✅ تم تحديث نوع الوصول لجميع المستخدمين غير المديرين إلى نظام الكاشير";

        // تحديث المديرين للنظام الإداري
        $update_admins = "UPDATE accounts SET access_type = 'admin_panel' WHERE role = 'admin'";
        $conn->query($update_admins);
        $results[] = "✅ تم تحديث المديرين للنظام الإداري";

        // فحص عدد المستخدمين المحدثين
        $count_query = "SELECT COUNT(*) as count FROM accounts WHERE role = 'user' AND access_type = 'cashier_system'";
        $count_result = $conn->query($count_query);
        $count = $count_result->fetch_assoc()['count'];
        $results[] = "📊 عدد المستخدمين في نظام الكاشير: $count";

        // فحص عدد المستخدمين في النظام الإداري
        $admin_count_query = "SELECT COUNT(*) as count FROM accounts WHERE access_type = 'admin_panel'";
        $admin_count_result = $conn->query($admin_count_query);
        $admin_count = $admin_count_result->fetch_assoc()['count'];
        $results[] = "📊 عدد المستخدمين في النظام الإداري: $admin_count";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في تكوين نوع الوصول: " . $e->getMessage();
    }

    return implode('<br>', $results);
}

function testSystem($conn, $key) {
    $results = [];

    try {
        // تضمين نظام الصلاحيات
        include_once 'permissions_system.php';
        $permissions_system = new PermissionsSystem($conn, $key);
        $results[] = "✅ تم إنشاء نظام الصلاحيات بنجاح";

        // اختبار فحص المدير
        $is_admin = $permissions_system->isAdmin();
        $results[] = $is_admin ? "✅ فحص المدير يعمل بشكل صحيح" : "❌ فحص المدير لا يعمل";

        // اختبار صلاحيات محددة
        $test_permissions = [
            ['accounts', 'manage', 'إدارة الحسابات'],
            ['purchase_invoices', 'access', 'الوصول لفواتير الشراء'],
            ['purchase_invoices', 'create', 'إنشاء فواتير الشراء']
        ];

        foreach ($test_permissions as $test) {
            $has_permission = $permissions_system->hasPermission($test[0], $test[1]);
            $status = $has_permission ? "✅" : "❌";
            $results[] = "$status {$test[2]}: " . ($has_permission ? 'مسموح' : 'غير مسموح');
        }

        // اختبار دوال المساعدة
        include_once 'auth_check.php';
        $has_any = hasAnyPermission('accounts', ['manage']);
        $results[] = $has_any ? "✅ دالة hasAnyPermission تعمل بشكل صحيح" : "❌ دالة hasAnyPermission لا تعمل";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في الاختبار: " . $e->getMessage();
    }

    return implode('<br>', $results);
}

function resetSystem($conn) {
    $results = [];

    try {
        $results[] = "🔄 بدء عملية إعادة تعيين النظام...";

        // تعطيل فحص المفاتيح الخارجية مؤقتاً
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");
        $results[] = "⚙️ تم تعطيل فحص المفاتيح الخارجية";

        // 1. حذف جميع البيانات من الجداول (بالترتيب الصحيح)
        $tables_to_clear = [
            'permission_logs' => 'سجل الصلاحيات',
            'user_permissions' => 'صلاحيات المستخدمين',
            'user_stores' => 'فروع المستخدمين',
            'role_permissions' => 'صلاحيات الأدوار',
            'module_permissions' => 'ربط الوحدات بالصلاحيات',
            'modules' => 'الوحدات',
            'permissions' => 'الصلاحيات',
            'roles' => 'الأدوار'
        ];

        foreach ($tables_to_clear as $table => $description) {
            if ($conn->query("DELETE FROM $table")) {
                $results[] = "🗑️ تم حذف جميع بيانات $description";
            } else {
                $results[] = "⚠️ تعذر حذف بيانات $description (قد تكون غير موجودة)";
            }
        }

        // 2. حذف الجداول نفسها (اختياري - يمكن تعطيله إذا كنت تريد الاحتفاظ بالهيكل)
        $tables_to_drop = [
            'permission_logs',
            'user_permissions',
            'user_stores',
            'role_permissions',
            'module_permissions',
            'modules',
            'permissions',
            'roles'
        ];

        foreach ($tables_to_drop as $table) {
            if ($conn->query("DROP TABLE IF EXISTS $table")) {
                $results[] = "🗑️ تم حذف جدول $table";
            } else {
                $results[] = "⚠️ تعذر حذف جدول $table: " . $conn->error;
            }
        }

        // 3. إزالة عمود access_type من جدول accounts (اختياري)
        $check_column = "SHOW COLUMNS FROM accounts LIKE 'access_type'";
        $result = $conn->query($check_column);
        if ($result && $result->num_rows > 0) {
            if ($conn->query("ALTER TABLE accounts DROP COLUMN access_type")) {
                $results[] = "🗑️ تم حذف عمود نوع الوصول من جدول الحسابات";
            } else {
                $results[] = "⚠️ تعذر حذف عمود نوع الوصول: " . $conn->error;
            }
        }

        // 4. إعادة تعيين AUTO_INCREMENT للجداول المتبقية
        $reset_tables = ['accounts', 'stores'];
        foreach ($reset_tables as $table) {
            $conn->query("ALTER TABLE $table AUTO_INCREMENT = 1");
        }
        $results[] = "🔄 تم إعادة تعيين عدادات الجداول";

        // إعادة تفعيل فحص المفاتيح الخارجية
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        $results[] = "⚙️ تم إعادة تفعيل فحص المفاتيح الخارجية";

        $results[] = "✅ تم إعادة تعيين النظام بالكامل - تم حذف جميع الجداول والبيانات";
        $results[] = "🔄 يمكنك الآن إعادة تشغيل التكوين من البداية";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في إعادة التعيين: " . $e->getMessage();

        // إعادة تفعيل فحص المفاتيح الخارجية في حالة الخطأ
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
    }

    return implode('<br>', $results);
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تكوين نظام الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .setup-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }

        .setup-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .step-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .step-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .step-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .step-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .setup-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin: 10px 5px;
        }

        .setup-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .setup-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }

        th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: bold;
        }

        .status-icon {
            font-size: 18px;
            margin-left: 10px;
        }

        .links-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }

        .nav-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            color: white;
        }

        .step-success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }

        .step-error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
            <i class="fas fa-cogs"></i> تكوين نظام الصلاحيات
        </h1>



        <!-- معلومات النظام -->
        <div class="setup-section">
            <div class="section-header">
                <i class="fas fa-info-circle"></i> معلومات نظام الصلاحيات
            </div>

            <div class="info-box">
                <h4>🎯 الهدف من النظام:</h4>
                <ul style="margin-right: 20px; line-height: 1.8;">
                    <li><strong>تحكم دقيق:</strong> إدارة صلاحيات المستخدمين على مستوى الوحدات والعمليات</li>
                    <li><strong>أمان محسن:</strong> حماية شاملة لجميع صفحات ووظائف النظام</li>
                    <li><strong>مرونة في الإدارة:</strong> تخصيص صلاحيات مختلفة لكل دور ومستخدم</li>
                    <li><strong>إدارة الفروع:</strong> تحديد الفروع المسموحة لكل مستخدم</li>
                    <li><strong>تسجيل العمليات:</strong> تتبع جميع تغييرات الصلاحيات</li>
                </ul>

                <h4>🏗️ مكونات النظام:</h4>
                <ul style="margin-right: 20px; line-height: 1.8;">
                    <li><strong>الوحدات (Modules):</strong> تمثل صفحات وأقسام النظام</li>
                    <li><strong>الصلاحيات (Permissions):</strong> العمليات المختلفة (عرض، إنشاء، تعديل، حذف، إلخ)</li>
                    <li><strong>الأدوار (Roles):</strong> مجموعات صلاحيات محددة مسبقاً</li>
                    <li><strong>صلاحيات المستخدمين:</strong> صلاحيات خاصة تتجاوز صلاحيات الدور</li>
                    <li><strong>صلاحيات الفروع:</strong> تحديد الفروع المسموحة لكل مستخدم</li>
                    <li><strong>نوع الوصول:</strong> تحديد النظام (إداري/كاشير) لكل مستخدم</li>
                </ul>

                <h4>🗄️ الجداول التي سيتم إنشاؤها:</h4>
                <ul style="margin-right: 20px; line-height: 1.8;">
                    <li><strong>roles:</strong> جدول الأدوار والمناصب</li>
                    <li><strong>modules:</strong> جدول وحدات النظام</li>
                    <li><strong>permissions:</strong> جدول الصلاحيات المختلفة</li>
                    <li><strong>module_permissions:</strong> ربط الوحدات بالصلاحيات المطلوبة</li>
                    <li><strong>role_permissions:</strong> ربط الأدوار بالصلاحيات</li>
                    <li><strong>user_permissions:</strong> صلاحيات خاصة للمستخدمين</li>
                    <li><strong>user_stores:</strong> ربط المستخدمين بالفروع</li>
                    <li><strong>permission_logs:</strong> سجل تغييرات الصلاحيات</li>
                    <li><strong>accounts (تحديث):</strong> إضافة عمود نوع الوصول</li>
                </ul>
            </div>
        </div>

        <!-- خطوات التكوين -->
        <div class="setup-section">
            <div class="section-header">
                <i class="fas fa-list-ol"></i> خطوات التكوين
            </div>

            <!-- شريط التقدم -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <span style="font-weight: bold;">تقدم التكوين:</span>
                    <span id="progressText">جاهز للبدء</span>
                </div>
                <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                    <div id="progressBar" style="background: linear-gradient(90deg, #28a745, #20c997); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>

            <div id="setupSteps">
                <div class="step-result" id="step1">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 1: فحص وإنشاء قاعدة البيانات والجداول المطلوبة</span>
                </div>

                <div class="step-result" id="step2">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 2: إنشاء الوحدات الأساسية</span>
                </div>

                <div class="step-result" id="step3">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 3: إنشاء الصلاحيات الأساسية</span>
                </div>

                <div class="step-result" id="step4">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 4: ربط الوحدات بالصلاحيات</span>
                </div>

                <div class="step-result" id="step5">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 5: تكوين صلاحيات الأدوار</span>
                </div>

                <div class="step-result" id="step6">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 6: إنشاء صلاحيات المدير</span>
                </div>

                <div class="step-result" id="step7">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 7: تكوين نوع الوصول للمستخدمين</span>
                </div>

                <div class="step-result" id="step8">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 8: اختبار النظام</span>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="setup-button" onclick="startSetup()">
                    <i class="fas fa-play"></i> بدء التكوين
                </button>
                <button class="setup-button" onclick="resetSystem()" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border: 2px solid #c0392b;">
                    <i class="fas fa-trash-alt"></i> حذف النظام بالكامل
                </button>
            </div>
        </div>

        <!-- نتائج التكوين -->
        <div class="setup-section" id="resultsSection" style="display: none;">
            <div class="section-header">
                <i class="fas fa-chart-bar"></i> نتائج التكوين
            </div>
            <div id="setupResults"></div>
        </div>

        <!-- روابط التنقل -->
        <div class="setup-section">
            <div class="section-header">
                <i class="fas fa-link"></i> روابط النظام
            </div>
            <div class="links-section">
                <a href="manage_permissions.php" class="nav-link">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                </a>
                <a href="stores.php" class="nav-link">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-users"></i> إدارة الحسابات
                </a>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 8;

        function updateProgress(step, status, message) {
            const stepElement = document.getElementById(`step${step}`);
            if (!stepElement) {
                console.error(`لم يتم العثور على العنصر step${step}`);
                return;
            }

            const icon = stepElement.querySelector('.status-icon');
            const text = stepElement.querySelector('span');

            if (!icon || !text) {
                console.error(`لم يتم العثور على العناصر الفرعية في step${step}`);
                return;
            }

            stepElement.className = `step-result step-${status}`;

            if (status === 'success') {
                icon.className = 'fas fa-check-circle status-icon';
                icon.style.color = '#155724';
            } else if (status === 'error') {
                icon.className = 'fas fa-times-circle status-icon';
                icon.style.color = '#721c24';
            } else if (status === 'warning') {
                icon.className = 'fas fa-exclamation-triangle status-icon';
                icon.style.color = '#856404';
            }

            if (message) {
                text.innerHTML = message;
            }

            // تحديث شريط التقدم
            const progress = (step / totalSteps) * 100;
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            if (progressBar) {
                progressBar.style.width = progress + '%';
            }

            if (progressText) {
                progressText.textContent = `الخطوة ${step} من ${totalSteps}`;
            }
        }

        async function startSetup() {
            const startButton = document.querySelector('button[onclick="startSetup()"]');
            const resultsSection = document.getElementById('resultsSection');

            if (startButton) {
                startButton.disabled = true;
            }

            if (resultsSection) {
                resultsSection.style.display = 'block';
            }

            try {
                // الخطوة 1: فحص وإنشاء قاعدة البيانات
                updateProgress(1, 'warning', 'الخطوة 1: جاري فحص وإنشاء الجداول...');
                await executeStep('check_database');

                // الخطوة 2: إنشاء الوحدات
                updateProgress(2, 'warning', 'الخطوة 2: جاري إنشاء الوحدات...');
                await executeStep('create_modules');

                // الخطوة 3: إنشاء الصلاحيات
                updateProgress(3, 'warning', 'الخطوة 3: جاري إنشاء الصلاحيات...');
                await executeStep('create_permissions');

                // الخطوة 4: ربط الوحدات بالصلاحيات
                updateProgress(4, 'warning', 'الخطوة 4: جاري ربط الوحدات بالصلاحيات...');
                await executeStep('setup_module_permissions');

                // الخطوة 5: تكوين صلاحيات الأدوار
                updateProgress(5, 'warning', 'الخطوة 5: جاري تكوين صلاحيات الأدوار...');
                await executeStep('setup_role_permissions');

                // الخطوة 6: إنشاء صلاحيات المدير
                updateProgress(6, 'warning', 'الخطوة 6: جاري إنشاء صلاحيات المدير...');
                await executeStep('setup_admin_permissions');

                // الخطوة 7: تكوين نوع الوصول
                updateProgress(7, 'warning', 'الخطوة 7: جاري تكوين نوع الوصول...');
                await executeStep('setup_access_types');

                // الخطوة 8: اختبار النظام
                updateProgress(8, 'warning', 'الخطوة 8: جاري اختبار النظام...');
                await executeStep('test_system');

                const progressText = document.getElementById('progressText');
                if (progressText) {
                    progressText.textContent = 'تم الانتهاء من التكوين بنجاح!';
                }

            } catch (error) {
                console.error('خطأ في التكوين:', error);
                alert('حدث خطأ أثناء التكوين: ' + error.message);
            }

            if (startButton) {
                startButton.disabled = false;
            }
        }

        async function executeStep(action) {
            let result;

            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=${action}`
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseText = await response.text();
                console.log('Response text:', responseText);

                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', responseText);
                    throw new Error('استجابة غير صالحة من الخادم');
                }

            } catch (fetchError) {
                console.error('Fetch error:', fetchError);
                throw new Error('فشل في الاتصال بالخادم: ' + fetchError.message);
            }

            if (result && result.success) {
                updateProgress(result.step, 'success', result.message);

                if (result.details) {
                    const resultsDiv = document.getElementById('setupResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML += `<div class="step-success">${result.details}</div>`;
                    }
                }
            } else {
                const errorMessage = result ? result.message : 'خطأ غير معروف';
                const stepNumber = result ? result.step : 0;

                updateProgress(stepNumber, 'error', errorMessage);

                // عرض تفاصيل إضافية للخطأ إذا كانت متوفرة
                if (result && result.file && result.line) {
                    const errorDetails = `الملف: ${result.file}, السطر: ${result.line}`;
                    const resultsDiv = document.getElementById('setupResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML += `<div class="step-error">📍 ${errorDetails}</div>`;
                    }
                }

                throw new Error(errorMessage);
            }

            // انتظار قصير لإظهار التقدم
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        async function resetSystem() {
            if (!confirm('⚠️ تحذير: هل أنت متأكد من إعادة تعيين النظام بالكامل؟\n\n' +
                        '🗑️ سيتم حذف:\n' +
                        '• جميع الجداول المنشأة\n' +
                        '• جميع الصلاحيات والأدوار\n' +
                        '• جميع بيانات المستخدمين والفروع\n' +
                        '• عمود نوع الوصول من جدول الحسابات\n\n' +
                        '⚡ هذا الإجراء لا يمكن التراجع عنه!')) {
                return;
            }

            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=reset_system'
                });

                const result = await response.json();

                if (result.success) {
                    alert('✅ تم إعادة تعيين النظام بالكامل بنجاح!\n\n' +
                          '🔄 سيتم إعادة تحميل الصفحة لبدء التكوين من جديد...');
                    location.reload();
                } else {
                    alert('❌ فشل في إعادة تعيين النظام: ' + result.message);
                }
            } catch (error) {
                alert('حدث خطأ: ' + error.message);
            }
        }
    </script>
</body>
</html>
