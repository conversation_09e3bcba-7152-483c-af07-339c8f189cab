<?php
/**
 * التحقق من حالة نظام الصلاحيات
 */

include 'db_connection.php';

echo "<h2>🔍 التحقق من حالة نظام الصلاحيات</h2>";

// 1. فحص الجداول
echo "<h3>1. الجداول المطلوبة:</h3>";
$required_tables = ['roles', 'modules', 'permissions', 'module_permissions', 'role_permissions', 'user_permissions', 'user_stores', 'permission_logs'];

foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
        $count = $count_result->fetch_assoc()['count'];
        echo "✅ $table - موجود ($count سجل)<br>";
    } else {
        echo "❌ $table - غير موجود<br>";
    }
}

// 2. فحص عمود access_type
echo "<h3>2. عمود نوع الوصول:</h3>";
$check_column = $conn->query("SHOW COLUMNS FROM accounts LIKE 'access_type'");
if ($check_column->num_rows > 0) {
    echo "✅ عمود access_type موجود<br>";
    
    // إحصائيات أنواع الوصول
    $admin_panel_count = $conn->query("SELECT COUNT(*) as count FROM accounts WHERE access_type = 'admin_panel'")->fetch_assoc()['count'];
    $cashier_system_count = $conn->query("SELECT COUNT(*) as count FROM accounts WHERE access_type = 'cashier_system'")->fetch_assoc()['count'];
    
    echo "📊 النظام الإداري: $admin_panel_count مستخدم<br>";
    echo "📊 نظام الكاشير: $cashier_system_count مستخدم<br>";
} else {
    echo "❌ عمود access_type غير موجود<br>";
}

// 3. فحص الوحدات
echo "<h3>3. الوحدات المطلوبة:</h3>";
$required_modules = [
    'dashboard', 'store_management', 'reports', 'categories', 'items', 
    'purchase_invoices', 'wholesale_invoices', 'inventory', 'accounts',
    'cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'
];

$missing_modules = [];
foreach ($required_modules as $module) {
    $result = $conn->query("SELECT COUNT(*) as count FROM modules WHERE module_name = '$module'");
    $exists = $result->fetch_assoc()['count'] > 0;
    
    if ($exists) {
        echo "✅ $module<br>";
    } else {
        echo "❌ $module<br>";
        $missing_modules[] = $module;
    }
}

// 4. فحص الصلاحيات
echo "<h3>4. الصلاحيات المطلوبة:</h3>";
$required_permissions = [
    'access', 'view', 'create', 'edit', 'delete', 'export', 'import', 
    'manage', 'approve', 'reports', 'create_purchase', 'create_sale', 
    'close_shift', 'edit_profile', 'switch_store'
];

$missing_permissions = [];
foreach ($required_permissions as $permission) {
    $result = $conn->query("SELECT COUNT(*) as count FROM permissions WHERE permission_name = '$permission'");
    $exists = $result->fetch_assoc()['count'] > 0;
    
    if ($exists) {
        echo "✅ $permission<br>";
    } else {
        echo "❌ $permission<br>";
        $missing_permissions[] = $permission;
    }
}

// 5. فحص ربط الوحدات بالصلاحيات
echo "<h3>5. ربط الوحدات بالصلاحيات:</h3>";
$module_permissions_count = $conn->query("SELECT COUNT(*) as count FROM module_permissions")->fetch_assoc()['count'];
echo "📊 عدد الروابط: $module_permissions_count<br>";

if ($module_permissions_count == 0) {
    echo "❌ لا توجد روابط - يحتاج إصلاح<br>";
} else {
    echo "✅ الروابط موجودة<br>";
}

// 6. فحص صلاحيات الأدوار
echo "<h3>6. صلاحيات الأدوار:</h3>";
$roles = ['admin', 'store_manager', 'purchaser', 'user', 'dealer'];

foreach ($roles as $role) {
    $result = $conn->query("
        SELECT COUNT(*) as count 
        FROM role_permissions rp 
        JOIN roles r ON rp.role_id = r.role_id 
        WHERE r.role_name = '$role' AND rp.granted = TRUE
    ");
    
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        if ($count > 0) {
            echo "✅ $role - $count صلاحية<br>";
        } else {
            echo "❌ $role - لا توجد صلاحيات<br>";
        }
    }
}

// 7. فحص صلاحيات نظام الكاشير
echo "<h3>7. صلاحيات نظام الكاشير للمستخدمين:</h3>";
$cashier_modules = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];

foreach ($cashier_modules as $module) {
    $result = $conn->query("
        SELECT COUNT(*) as count 
        FROM role_permissions rp 
        JOIN roles r ON rp.role_id = r.role_id 
        JOIN modules m ON rp.module_id = m.module_id
        WHERE r.role_name = 'user' AND m.module_name = '$module' AND rp.granted = TRUE
    ");
    
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        if ($count > 0) {
            echo "✅ $module - $count صلاحية<br>";
        } else {
            echo "❌ $module - لا توجد صلاحيات<br>";
        }
    }
}

// 8. اختبار نظام الصلاحيات
echo "<h3>8. اختبار النظام:</h3>";
try {
    include_once 'permissions_system.php';
    include_once 'encryption_functions.php';
    
    if (file_exists(__DIR__ . '/.env')) {
        $dotenv = parse_ini_file(__DIR__ . '/.env');
        foreach ($dotenv as $key => $value) {
            putenv("$key=$value");
        }
    }
    
    $key = getenv('ENCRYPTION_KEY');
    $permissions_system = new PermissionsSystem($conn, $key);
    echo "✅ تم إنشاء نظام الصلاحيات بنجاح<br>";
    
    // اختبار بعض الوظائف
    $modules = $permissions_system->getModules();
    echo "✅ تم جلب " . count($modules) . " وحدة<br>";
    
    $permissions = $permissions_system->getPermissions();
    echo "✅ تم جلب " . count($permissions) . " صلاحية<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في النظام: " . $e->getMessage() . "<br>";
}

// 9. الخلاصة
echo "<h3>9. الخلاصة:</h3>";
$total_issues = count($missing_modules) + count($missing_permissions);

if ($total_issues == 0 && $module_permissions_count > 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
    echo "<strong>✅ نظام الصلاحيات مكتمل وجاهز للاستخدام!</strong><br>";
    echo "جميع الجداول والبيانات موجودة ومكتملة.";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>";
    echo "<strong>❌ يحتاج النظام لإصلاحات:</strong><br>";
    if (count($missing_modules) > 0) {
        echo "- وحدات ناقصة: " . implode(', ', $missing_modules) . "<br>";
    }
    if (count($missing_permissions) > 0) {
        echo "- صلاحيات ناقصة: " . implode(', ', $missing_permissions) . "<br>";
    }
    if ($module_permissions_count == 0) {
        echo "- ربط الوحدات بالصلاحيات ناقص<br>";
    }
    echo "</div>";
    
    echo "<br><strong>الحلول المقترحة:</strong><br>";
    echo "1. تشغيل ملف الإصلاح السريع: <a href='quick_fix_permissions.php'>quick_fix_permissions.php</a><br>";
    echo "2. تشغيل ملف التكوين الكامل: <a href='setup_permissions_system.php'>setup_permissions_system.php</a><br>";
}

$conn->close();
?>
